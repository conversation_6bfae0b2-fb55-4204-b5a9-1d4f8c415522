FROM openjdk:8-jdk-slim

# Build arguments para las variables de entorno
ARG DEFAULT_ERROR_URL
ARG DB_USER
ARG DB_PASSWORD

# Convertir build args a environment variables
ENV DEFAULT_ERROR_URL=${DEFAULT_ERROR_URL}
ENV DB_USER=${DB_USER}
ENV DB_PASSWORD=${DB_PASSWORD}

RUN apt-get update && apt-get install -y --no-install-recommends \
    fontconfig \
    fonts-dejavu \
    && rm -rf /var/lib/apt/lists/*

# Crear usuario
RUN useradd -m -d /home/<USER>/bin/bash appmobid

USER appmobid

# Copiar el JAR de la aplicación
COPY target/shorturl.jar shorturl.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "shorturl.jar"]
