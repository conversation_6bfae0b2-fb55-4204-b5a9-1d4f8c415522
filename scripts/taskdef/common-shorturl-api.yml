config:
  service: common-shorturl-api
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: converted-audios
    containerDefinitions:
      - name: app
        image: ${SHORTURL_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "common-shorturl-api"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "common-shorturl-api"
    

