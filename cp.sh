#/bin/sh
LHOME=/home/<USER>/workspaces/git-athlas/SHORT-URL/web_service
LAPP=/home/<USER>/apps
DOCKER_BUILD=$LAPP/publish_docker
APP=shorturl
echo "#====== inicio ======#"
echo "#------ copiando jar ------#"
SOURCE=$LHOME/$APP/target
TARGET=$DOCKER_BUILD/$APP

echo "cp $SOURCE/shorturl.jar $TARGET/shorturl.jar"
cp $SOURCE/shorturl.jar $TARGET/shorturl.jar

echo ""
echo ""

echo "#------copiando properties------#"
SOURCE=$LHOME/properties-tomcat/shorturl/properties
TARGET=$LAPP/data/$APP/properties

echo "cp $SOURCE/application.properties $TARGET/application.properties"
cp $SOURCE/application.properties $TARGET/application.properties

echo "cp $SOURCE/logback.xml $TARGET/logback.xml"
cp $SOURCE/logback.xml $TARGET/logback.xml

echo "cp $SOURCE/msg.properties $TARGET/msg.properties"
cp $SOURCE/msg.properties $TARGET/msg.properties
echo "#====== fin ======#"