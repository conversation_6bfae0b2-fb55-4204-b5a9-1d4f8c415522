#--------------------------#
# configuracion standalone #
#--------------------------#
server.port=8080
server.servlet.context-path=/shorturl
#------------------------------------#
#configutracion de pool de conexiones#
#------------------------------------#
spring.datasource.url=************************************************
spring.datasource.username=${DB_USER}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
spring.datasource.dbcp2.initial-size=1
spring.datasource.dbcp2.max-idle=1
spring.datasource.dbcp2.max-total=5
spring.datasource.dbcp2.validation-query=SELECT 1
spring.datasource.type=org.apache.commons.dbcp2.BasicDataSource
#----------------------------------------------------#
#              configuracion de hibernate            #
#----------------------------------------------------#
# esto es para evitar un error generado por los datasource multiple
spring.jmx.enabled=false
spring.jpa.database-platform=org.hibernate.dialect.MariaDBDialect
spring.jpa.hibernate.ddl-auto=validate
#para no generar los ddl de modificacion de la base de datos con los entity
spring.jpa.generate-ddl=false
#------------- mostrar sql de jpa entity-------------#
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql=true
#------------- mostrar sql de jpa entity-------------#
spring.jpa.open-in-view=false

#----------------------------------------------------#
#              configuracion de java Melody            #
#----------------------------------------------------#
# Enable JavaMelody auto-configuration (optional, default: true)
javamelody.enabled=true
# Data source names to exclude from monitoring (optional, comma-separated)
javamelody.excluded-datasources=secretSource,topSecretSource
# Enable monitoring of Spring services and controllers (optional, default: true)
javamelody.spring-monitoring-enabled=true
# Initialization parameters for JavaMelody (optional)
# See: https://github.com/javamelody/javamelody/wiki/UserGuide#6-optional-parameters
#    log http requests:
javamelody.init-parameters.log=true
#    to exclude images, css, fonts and js urls from the monitoring:
# javamelody.init-parameters.url-exclude-pattern=(/webjars/.*|/css/.*|/images/.*|/fonts/.*|/js/.*)
#    to aggregate digits in http requests:
# javamelody.init-parameters.http-transform-pattern: \d+
#    to add basic auth:
# javamelody.init-parameters.authorized-users=admin:pwd
#    to change the default storage directory:
# javamelody.init-parameters.storage-directory=/tmp/javamelody
#    to change the default "/monitoring" path:
# javamelody.init-parameters.monitoring-path=/admin/see
#------------------------------------#
#       otras configuraciones        #
#------------------------------------#
env.default.error.url=${DEFAULT_ERROR_URL}
env.prefix.url=www.
path.property=/home/<USER>/properties-docker

#-----------------configuracion del actuator------------------#
# Solo exponer el endpoint 'health'
management.endpoints.web.exposure.include=health

# Ruta base del actuator (opcional, este es el valor por defecto)
management.endpoints.web.base-path=/actuator
