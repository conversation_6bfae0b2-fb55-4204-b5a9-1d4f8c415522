package cl.mobid.global.shorturl.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Component;

import cl.mobid.global.shorturl.dto.AccessDto;

@Component
public class AccessDao {
	private final String SQL_SEL_ACCESS = "call sp_shorturl_sel_access_by_token( ? ); ";
	
	@Autowired
	JdbcTemplate jdbcTemplate;
	
	public AccessDto findByToken(String token) {
		String sql = SQL_SEL_ACCESS;
		
		AccessDto dto = jdbcTemplate.query( new PreparedStatementCreator() {
			@Override
			public PreparedStatement createPreparedStatement(Connection con) throws SQLException {
				PreparedStatement ps = con.prepareStatement(sql);
				ps.setString(1, token);
				return ps;
			}
		} , new ResultSetExtractor<AccessDto>() {
		    @Override
            public AccessDto extractData(ResultSet rs) throws SQLException, DataAccessException {
            	if(rs.next()) {
            		AccessDto e = new AccessDto();
    				e.setId(rs.getInt("id_shudom"));
    				e.setCode(rs.getString("code"));
    				e.setIdApplication(rs.getInt("id_application"));
    				e.setIdDomain(rs.getInt("id_shudom"));
    				e.setDefaultValidDays(rs.getInt("default_valid_days"));
    				return e;
            	}else {
            		return null;
            	}
            }
        });
        return dto;
		
//		Connection conn = null;
//		PreparedStatement stm = null;
//		ResultSet rs = null;
//		try {
//			conn = this.getDataSource().getConnection();
//			stm = conn.prepareStatement(sql);
//			stm.setString(1, token);
//			rs = stm.executeQuery();
//			while (rs.next()) {
//				AccessDto e = new AccessDto();
//				e.setId(rs.getInt("id_shudom"));
//				e.setCode(rs.getString("code"));
//				e.setIdApplication(rs.getInt("id_application"));
//				e.setIdDomain(rs.getInt("id_shudom"));
//				e.setDefaultValidDays(rs.getInt("default_valid_days"));
//				return e;
//			}
//		} catch (SQLException e) {
//			this.log.error(e.getMessage(), e);
//		} finally {
//			this.close(conn, stm, rs);
//		}
//		return null;
	}
}
