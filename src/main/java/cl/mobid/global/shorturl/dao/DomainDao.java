package cl.mobid.global.shorturl.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Component;

import cl.mobid.global.shorturl.dto.DomainDto;

@Component
public class DomainDao 
//extends DbConection 
{
	
	private final String SQL_SEL_DOMAIN = "call sp_shorturl_sel_domain_by_id( ? ); ";
	private final String SQL_SEL_SEQUENCE_BY_ID_DOMAIN = "call sp_shorturl_sel_sequence_by_id_domain( ? ); ";
	@Autowired
	JdbcTemplate jdbcTemplate;
	
	public DomainDto findById(int id) {
		DomainDto dto = jdbcTemplate.query( new PreparedStatementCreator() {
			@Override
			public PreparedStatement createPreparedStatement(Connection con) throws SQLException {
				PreparedStatement ps = con.prepareStatement(SQL_SEL_DOMAIN);
				ps.setInt(1, id);
				return ps;
			}
		} , new ResultSetExtractor<DomainDto>() {
		    @Override
            public DomainDto extractData(ResultSet rs) throws SQLException, DataAccessException {
            	if(rs.next()) {
            		DomainDto e = new DomainDto();
            	    e.setId(rs.getInt("id_shudom"));
            	    e.setDomain(rs.getString("short_domain"));
            		return e;
            	}else {
            		return null;
            	}
            }
        });
        return dto;
				
//		String sql = SQL_SEL_DOMAIN;
//		Connection conn = null;
//		PreparedStatement stm = null;
//		ResultSet rs = null;
//		try {
//			conn = this.getDataSource().getConnection();
//			stm = conn.prepareStatement(sql);
//			stm.setInt(1, id);
//			rs = stm.executeQuery();
//			while (rs.next()) {
//				DomainDto e = new DomainDto();
//				e.setId(rs.getInt("id_shudom"));
//				e.setDomain(rs.getString("short_domain"));
//				return e;
//			}
//		} catch (SQLException e) {
//			this.log.error(e.getMessage(), e);
//		} finally {
//			this.close(conn, stm, rs);
//		}
//		return null;
	}

	public int getSequenceByDomain(int id) {
		
		Integer dto = jdbcTemplate.query( new PreparedStatementCreator() {
			@Override
			public PreparedStatement createPreparedStatement(Connection con) throws SQLException {
				PreparedStatement ps = con.prepareStatement(SQL_SEL_SEQUENCE_BY_ID_DOMAIN);
				ps.setInt(1, id);
				return ps;
			}
		} , new ResultSetExtractor<Integer>() {
		    @Override
            public Integer extractData(ResultSet rs) throws SQLException, DataAccessException {
            	if(rs.next()) {
            		Integer e = rs.getInt("seq");
            		return e;
            	}else {
            		return null;
            	}
            }
        });
		return dto !=null ? dto.intValue() : 0;
		
//		String sql = SQL_SEL_SEQUENCE_BY_ID_DOMAIN;
//		Connection conn = null;
//		PreparedStatement stm = null;
//		ResultSet rs = null;
//		try {
//			conn = this.getDataSource().getConnection();
//			stm = conn.prepareStatement(sql);
//			stm.setInt(1, id);
//			rs = stm.executeQuery();
//			while (rs.next()) {
//				int sequence = rs.getInt("seq");
//				return sequence;
//			}
//		} catch (SQLException e) {
//			this.log.error(e.getMessage(), e);
//		} finally {
//			this.close(conn, stm, rs);
//		}
//		return -1;
	}
}
