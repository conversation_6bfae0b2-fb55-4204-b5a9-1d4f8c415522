package cl.mobid.global.shorturl.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Component;

import cl.mobid.global.shorturl.dto.DataDto;

@Component
public class DataDao {
	private final String SQL_SEL_DATA = "call sp_shorturl_sel_data_by_domain_and_sequence( ?, ? );";
	private final String SQL_INS_DATA = "call sp_shorturl_ins_data(?, ?, ?, ?, ?, ?);";
	
	@Autowired
	JdbcTemplate jdbcTemplate;
	
	public int insert(DataDto dto, String token) {
		String sql = SQL_INS_DATA;
		Integer id = jdbcTemplate.query( new PreparedStatementCreator() {
			@Override
			public PreparedStatement createPreparedStatement(Connection con) throws SQLException {
				PreparedStatement ps = con.prepareStatement(sql);
				ps.setString(1, dto.getShortUrl());
				ps.setString(2, dto.getLargeUrl());
				ps.setInt(3, dto.getValidDays());
				ps.setString(4, token);
				ps.setInt(5, dto.getIdDomain());
				ps.setString(6, dto.getSequence());
				return ps;
			}
		} , new ResultSetExtractor<Integer>() {
		    @Override
            public Integer extractData(ResultSet rs) throws SQLException, DataAccessException {
            	if(rs.next()) {
            		Integer e = rs.getInt("id");
            		return e;
            	}else {
            		return null;
            	}
            }
        });
		return id !=null ? id.intValue() : 0;
		
//		Connection conn = null;
//		PreparedStatement stm = null;
//		ResultSet rs = null;
//		try {
//			conn = this.getDataSource().getConnection();
//			stm = conn.prepareStatement(sql);
//			stm.setString(1, dto.getShortUrl());
//			stm.setString(2, dto.getLargeUrl());
//			stm.setInt(3, dto.getValidDays());
//			stm.setString(4, token);
//			stm.setInt(5, dto.getIdDomain());
//			stm.setString(6, dto.getSequence());
//			rs = stm.executeQuery();
//			while (rs.next()) {
//				int id = rs.getInt("id");
//				return id;
//			}
//		} catch (SQLException e) {
//			this.log.error(e.getMessage(), e);
//		} finally {
//			this.close(conn, stm, rs);
//		}
//		return -1;
	}
	
	public DataDto findByShortUrl(String domain, String sequence) {
		String sql = SQL_SEL_DATA;
		
		DataDto dto = jdbcTemplate.query( new PreparedStatementCreator() {
			@Override
			public PreparedStatement createPreparedStatement(Connection con) throws SQLException {
				PreparedStatement ps = con.prepareStatement(sql);
				ps.setString(1, domain);
				ps.setString(2, sequence);
				return ps;
			}
		} , new ResultSetExtractor<DataDto>() {
		    @Override
            public DataDto extractData(ResultSet rs) throws SQLException, DataAccessException {
            	if(rs.next()) {
            		DataDto e = new DataDto();
    				e.setId(rs.getInt("id_shudata"));
    				e.setShortUrl(rs.getString("shorturl"));
    				e.setLargeUrl(rs.getString("largeurl"));
    				e.setCreateDate(rs.getString("create_date"));
    				e.setValidDays(rs.getInt("valid_days"));
    				e.setIdAccess(rs.getInt("id_shuacc"));
    				e.setIdDomain(rs.getInt("id_shudom"));
    				e.setSequence(rs.getString("sequence"));
            		return e;
            	}else {
            		return null;
            	}
            }
        });
        return dto;
		
//		Connection conn = null;
//		PreparedStatement stm = null;
//		ResultSet rs = null;
//		try {
//			conn = this.getDataSource().getConnection();
//			stm = conn.prepareStatement(sql);
//			stm.setString(1, domain);
//			stm.setString(2, sequence);
//			rs = stm.executeQuery();
//			while (rs.next()) {
//				DataDto e = new DataDto();
//				e.setId(rs.getInt("id_shudata"));
//				e.setShortUrl(rs.getString("shorturl"));
//				e.setLargeUrl(rs.getString("largeurl"));
//				e.setCreateDate(rs.getString("create_date"));
//				e.setValidDays(rs.getInt("valid_days"));
//				e.setIdAccess(rs.getInt("id_shuacc"));
//				e.setIdDomain(rs.getInt("id_shudom"));
//				e.setSequence(rs.getString("sequence"));
//				return e;
//			}
//		} catch (SQLException e) {
//			this.log.error(e.getMessage(), e);
//		} finally {
//			this.close(conn, stm, rs);
//		}
//		return null;
	}
	
}
