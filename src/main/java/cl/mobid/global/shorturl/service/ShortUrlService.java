package cl.mobid.global.shorturl.service;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import cl.mobid.global.shorturl.config.PropertiesConfig;
import cl.mobid.global.shorturl.dao.AccessDao;
import cl.mobid.global.shorturl.dao.DataDao;
import cl.mobid.global.shorturl.dao.DomainDao;
import cl.mobid.global.shorturl.dto.AccessDto;
import cl.mobid.global.shorturl.dto.DataDto;
import cl.mobid.global.shorturl.dto.DomainDto;
import cl.mobid.global.shorturl.dto.ListUrlDto;
import cl.mobid.global.shorturl.dto.ResponseDto;
import cl.mobid.global.shorturl.dto.UrlDto;
import cl.mobid.global.shorturl.util.Constant;
import cl.mobid.global.shorturl.util.RandomSequence;

@Service
public class ShortUrlService {

	private Logger log = LoggerFactory.getLogger(this.getClass());

	@Autowired
	DataDao dataDao;
	
	@Autowired
	DomainDao domainDao;
	
	@Autowired
	AccessDao accessDao;
	
	@Value("${env.default.error.url:https://qa-ares.mobid.cl/error}")
	String redirectUrl;
	
	@Value("${env.prefix.url}")
	String prefixUrl;
	
	public void redirectToLargeUrl(HttpServletRequest request, HttpServletResponse response, String code) throws IOException {
		this.log.debug("#-----------------------#");
		String domain = request.getServerName().toString();
		this.log.debug("Call-Domain: "+domain);
		domain = domain.replace(prefixUrl, "");
		if(code !=null && code.length()>0) {
			if(code.length()<=4) {
				DataDto dataDto = dataDao.findByShortUrl(domain, code);
				if (dataDto != null) {
					redirectUrl = dataDto.getLargeUrl();
					this.log.debug("Find-Domain:" + domain);
					this.log.debug("Sequence:" + code);
					this.log.debug("Short-url:" + request.getRequestURL());
					this.log.debug("Large-url:" + redirectUrl);
					this.log.debug("Rediriguiendo a Url:" + redirectUrl);
					this.log.debug("#-----------------------#");
					response.sendRedirect(redirectUrl);
				}else {
					this.log.debug(String.format( "shorturl no encontrada: %s/%s ",domain,code));
					response.sendError(HttpServletResponse.SC_FORBIDDEN);
				}
			}else {
				this.log.debug(String.format( "code exede el largo maximo de 4 caracteres"));
				response.sendError(HttpServletResponse.SC_FORBIDDEN);
			}
			
		}else {
			this.log.debug(String.format( "code de url corta es null"));
			response.sendError(HttpServletResponse.SC_FORBIDDEN);
		}
	}
	
	public ListUrlDto getRequest() {
		ListUrlDto list = new ListUrlDto();
		UrlDto url = null;
		url = new UrlDto();
		url.setCode("abc");
		url.setDomain("mobid.cl");
		url.setUrl("mobid.cl/zxcas");
		url.setSequence("zxcas");
		url.setValidDays(2);
		list.getList().add(url);
		url = new UrlDto();
		url.setCode("abc2");
		url.setDomain("mobid.cl");
		url.setUrl("mobid.cl/zxca2");
		url.setSequence("zxca2");
		url.setValidDays(3);
		list.getList().add(url);
		return list;
	}
	
	public ResponseDto generateShortUrl(ListUrlDto list, HttpHeaders headers) {
		PropertiesConfig prop = PropertiesConfig.getInstancia();
		UrlDto shorturl = null;
		ListUrlDto listResponse = new ListUrlDto();
		ResponseDto response = new ResponseDto();
		int validDays = 0;
		String sequenceText = null;
		try {
			this.log.debug("########################");
			List<String> listHeaders = headers.get(Constant.HEADER_TOKEN);
			if (listHeaders != null && listHeaders.size() > 0) {
				String token = listHeaders.get(0);
				this.log.debug("Token: "+token);
				if (token != null && token.length() > 0) {
					AccessDto accessDto = accessDao.findByToken(token);
					if (accessDto != null) {
						DomainDto domainDto = domainDao.findById(accessDto.getIdDomain());
						if (domainDto != null) {
							domainDto.setDomain(this.prefixUrl+domainDto.getDomain());
							this.log.debug("Domain: "+domainDto.getDomain());
							if(list!=null) {
								for(UrlDto dto : list.getList()) {
									this.log.debug("#-----------------------#");
									int sequenceNumber = domainDao.getSequenceByDomain(accessDto.getIdDomain());
									sequenceText = RandomSequence.getMap(sequenceNumber);
									this.log.debug("Sequence: "+sequenceText);
									if(dto.getValidDays() > 0) {
										validDays = dto.getValidDays();
									}else {
										validDays = accessDto.getDefaultValidDays();
									}
									this.log.debug("ValidDays: "+validDays);
									
									DataDto dataDto = new DataDto();
									dataDto.setLargeUrl(dto.getUrl());
									dataDto.setShortUrl(domainDto.getDomain() + "/" + sequenceText);
									dataDto.setSequence(sequenceText);
									dataDto.setIdDomain(domainDto.getId());
									dataDto.setValidDays(validDays);
									int id = dataDao.insert(dataDto, token);
									if (id > 0) {
										shorturl = new UrlDto();
										shorturl.setCode(dto.getCode());
										shorturl.setUrl(domainDto.getDomain() + "/" + sequenceText);
										shorturl.setDomain(domainDto.getDomain());
										shorturl.setSequence(sequenceText);
										shorturl.setValidDays(validDays);
										log.debug("Se genera url: "+shorturl.getUrl());
										listResponse.getList().add(shorturl);
									}
								}
								response.setData(listResponse);
								response.setCode(0);
							} else {
								log.error("No hay url para procesar.");
								response.setCode(1005);
							}
						} else {
							log.error("Token sin dominio");
							response.setCode(1004);
						}
					} else {
						this.log.error("token no encontrado en BBDD");
						response.setCode(1003);
					}
				} else {
					log.error("Token null o en blanco");
					response.setCode(1002);
				}
			} else {
				log.error("sin token");
				response.setCode(1001);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			response.setCode(1000);
		}
		String message = prop.getPropiedadType("shorturl.generate.code."+response.getCode());
		response.setMessage(message);
		this.log.debug("########################");
		return response;
	}

	public ResponseDto getInfo(UrlDto dto, HttpHeaders headers) {
		PropertiesConfig prop = PropertiesConfig.getInstancia();
		ResponseDto responseDto = new ResponseDto();
		try {
			this.log.debug("#-----------------------#");
			List<String> listHeaders = headers.get(Constant.HEADER_TOKEN);
			if (listHeaders != null && listHeaders.size() > 0) {
				String token = listHeaders.get(0);
				this.log.debug("Token: "+token);
				if (token != null && token.length() > 0) {
					AccessDto accessDto = accessDao.findByToken(token);
					if (accessDto != null) {
						String[] splitUrl = dto.getUrl().split("/");
						splitUrl[0] = splitUrl[0].replaceAll(prefixUrl, "");
						this.log.debug("Url enviada: " + dto.getUrl());
						this.log.debug("Domain enviado: " + splitUrl[0]);
						this.log.debug("Sequence enviada: " + splitUrl[1]);
						this.log.debug("#-----------------------#");
						
						DataDto dataDto = dataDao.findByShortUrl(splitUrl[0], splitUrl[1]);
						if(dataDto!=null) {
							responseDto.setCode(0);
							responseDto.setData(dataDto);
						} else {
							this.log.error("No se encuentra la informacion de la url");
							responseDto.setCode(1004);
						}
					} else {
						this.log.error("token no encontrado en BBDD");
						responseDto.setCode(1003);
					}
				} else {
					log.error("Token null o en blanco");
					responseDto.setCode(1002);
				}
			} else {
				log.error("sin token");
				responseDto.setCode(1001);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			responseDto.setCode(1000);
		}
		String message = prop.getPropiedadType("shorturl.info.code."+responseDto.getCode());
		responseDto.setMessage(message);
		return responseDto;
	}
}
