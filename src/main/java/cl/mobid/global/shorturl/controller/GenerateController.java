package cl.mobid.global.shorturl.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cl.mobid.global.shorturl.dto.ListUrlDto;
import cl.mobid.global.shorturl.dto.ResponseDto;
import cl.mobid.global.shorturl.dto.UrlDto;
import cl.mobid.global.shorturl.service.ShortUrlService;

@RestController
@RequestMapping("api/generate")
public class GenerateController {
	
	@Autowired
	ShortUrlService service;
	
	@PostMapping("url")
	public ResponseDto generateShortUrlPost(
			@RequestBody ListUrlDto list
			, @RequestHeader HttpHeaders headers
		) {
		return service.generateShortUrl(list, headers);
	}
	
	@PostMapping("info")
	public ResponseDto infoShortUrlGet(
			@RequestBody UrlDto dto 
			, @RequestHeader HttpHeaders headers
			) {
		return service.getInfo(dto, headers);
	}
	
	@GetMapping("request")
	public ListUrlDto getRequest(
			) {
		return service.getRequest();
	}
	
	
}
