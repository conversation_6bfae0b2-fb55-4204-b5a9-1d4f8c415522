package cl.mobid.global.shorturl.controller;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cl.mobid.global.shorturl.service.ShortUrlService;

@RestController
@RequestMapping("api/access")
public class AccessController {
	
	@Autowired
	ShortUrlService service;
	
	@GetMapping("{code}")
	public void redirectUrl(
			@PathVariable("code") String code
			, HttpServletRequest request
			, HttpServletResponse response	
		) throws IOException {
		service.redirectToLargeUrl(request, response, code);
	}
}
