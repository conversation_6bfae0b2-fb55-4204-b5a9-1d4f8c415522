package cl.mobid.global.shorturl;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
public class ShorturlApplication extends SpringBootServletInitializer {

	@Bean
	InitBean initBean() {
		return new InitBean();
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder springApplicationBuilder) {
		springApplicationBuilder.sources(ShorturlApplication.class);
		return springApplicationBuilder;
	}

	public static void main(String[] args) {
		SpringApplicationBuilder springApplicationBuilder = new SpringApplicationBuilder(ShorturlApplication.class);
		springApplicationBuilder.sources(ShorturlApplication.class);
		springApplicationBuilder.run(args);
	}
}
