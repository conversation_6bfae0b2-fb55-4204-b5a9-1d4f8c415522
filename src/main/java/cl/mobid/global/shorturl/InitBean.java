package cl.mobid.global.shorturl;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import cl.mobid.global.shorturl.config.PropertiesConfig;

public class InitBean {
	private static Logger log = LoggerFactory.getLogger(InitBean.class);
	
	@Value("${path.property}")
	String path;
	
	@PostConstruct
    public void init() {
		InitBean.log.info("Aplicacion inicializada ");
		log.info("path: "+path);
		//PropertiesConfig.PATH=path;
    	//PropertiesConfig.getInstancia();
    }

    @PreDestroy
    public void destroy() {
    	InitBean.log.info("Aplicacion Bajada");
    }
}
