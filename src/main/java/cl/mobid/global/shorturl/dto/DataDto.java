package cl.mobid.global.shorturl.dto;

public class DataDto {
	private int id;
	private String shortUrl;
	private int idDomain;
	private String sequence;
	private String largeUrl;
	private String createDate;
	private int validDays;
	private int idAccess;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getShortUrl() {
		return shortUrl;
	}
	public void setShortUrl(String shortUrl) {
		this.shortUrl = shortUrl;
	}
	public String getLargeUrl() {
		return largeUrl;
	}
	public void setLargeUrl(String largeUrl) {
		this.largeUrl = largeUrl;
	}
	public String getCreateDate() {
		return createDate;
	}
	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}
	public int getValidDays() {
		return validDays;
	}
	public void setValidDays(int validDays) {
		this.validDays = validDays;
	}
	public int getIdAccess() {
		return idAccess;
	}
	public void setIdAccess(int idAccess) {
		this.idAccess = idAccess;
	}
	public int getIdDomain() {
		return idDomain;
	}
	public void setIdDomain(int idDomain) {
		this.idDomain = idDomain;
	}
	public String getSequence() {
		return sequence;
	}
	public void setSequence(String sequence) {
		this.sequence = sequence;
	}
	
	
}
