package cl.mobid.global.shorturl.dto;

public class AccessDto {
	private int id;
	private String code;
	private int idApplication;
	private int idDomain;
	private int defaultValidDays;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public int getIdApplication() {
		return idApplication;
	}

	public void setIdApplication(int idApplication) {
		this.idApplication = idApplication;
	}

	public int getIdDomain() {
		return idDomain;
	}

	public void setIdDomain(int idDomain) {
		this.idDomain = idDomain;
	}

	public int getDefaultValidDays() {
		return defaultValidDays;
	}

	public void setDefaultValidDays(int defaultValidDays) {
		this.defaultValidDays = defaultValidDays;
	}

}
