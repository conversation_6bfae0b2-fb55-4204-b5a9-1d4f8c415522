package cl.mobid.global.shorturl.config;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import cl.mobid.global.shorturl.util.Constant;

public class PropertiesConfig {
	private Properties propertyES;
	private Logger log = LogManager.getLogger(this.getClass());
	private static PropertiesConfig instancia = null;
	
	
	private PropertiesConfig() {
		propertyES = new Properties();
		try (Reader reader = crearReaderProperty("msg.properties")) {
			propertyES.load(reader);
		} catch (IOException e) {
			this.log.error("Error loading properties file", e);
		}
	}

	private Reader crearReaderProperty(String filename) throws IOException {
		this.log.info("Loading resource file: " + filename);

		ClassLoader classLoader = getClass().getClassLoader();
		InputStream inputStream = classLoader.getResourceAsStream(filename);

		if (inputStream == null) {
			throw new IOException("Resource not found: " + filename);
		}

		return new InputStreamReader(inputStream, Constant.PROP_FILE_UTF8);
	}

	public static PropertiesConfig getInstancia() {
		if (instancia == null) {
			instancia = new PropertiesConfig();
		}
		return instancia;
	}

	public String getPropiedadType(String clave) {
		String texto = propertyES.getProperty(clave);
		/* 
		esto tendra uso si se implementa idiomas de mensajes
		
		switch (type) {
		case Constant.PROP_TYPE_MSG:
			texto = propertyES.getProperty(clave);
			break;
		default:
			texto = "";
			break;
		}
		*/
		return texto;
	}
}
